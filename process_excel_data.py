#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据处理脚本
功能：处理test.xlsx文件中"清单"工作表的"处理过程"列数据
提取特定内容到新的列中
"""

import re
import pandas as pd
from openpyxl import load_workbook
from openpyxl.utils.dataframe import dataframe_to_rows
import os


def extract_content_between_markers(text):
    """
    基于实际数据格式提取处理内容
    从"一、处理结果："或"一、处理情况："部分提取内容

    Args:
        text (str): 输入文本

    Returns:
        str: 提取的处理内容，如果没有找到则返回空字符串
    """
    if not text or not isinstance(text, str):
        return ""

    # 尝试提取"一、处理结果："后的内容
    pattern1 = r'一、处理结果：(.*?)(?=二、|三、|$)'
    match1 = re.search(pattern1, text, re.DOTALL)

    if match1:
        content = match1.group(1).strip()
        # 清理内容，移除多余的换行和空格
        content = re.sub(r'\s+', ' ', content)
        return content

    # 如果没找到"处理结果"，尝试"处理情况"
    pattern2 = r'一、处理情况：(.*?)(?=二、|三、|$)'
    match2 = re.search(pattern2, text, re.DOTALL)

    if match2:
        content = match2.group(1).strip()
        content = re.sub(r'\s+', ' ', content)
        return content

    return ""


def extract_feedback_result(text):
    """
    基于实际数据格式提取反馈结果
    从"二、核查情况："部分提取内容

    Args:
        text (str): 输入文本

    Returns:
        str: 提取的反馈结果，如果没有找到则返回空字符串
    """
    if not text or not isinstance(text, str):
        return ""

    # 提取"二、核查情况："后的内容
    pattern = r'二、核查情况：(.*?)(?=三、|$)'
    match = re.search(pattern, text, re.DOTALL)

    if match:
        content = match.group(1).strip()
        # 清理内容，移除多余的换行和空格
        content = re.sub(r'\s+', ' ', content)
        return content

    return ""


def process_excel_file(input_file_path, output_file_path=None):
    """
    处理Excel文件，提取处理过程列中的特定内容
    
    Args:
        input_file_path (str): 输入Excel文件路径
        output_file_path (str): 输出Excel文件路径，如果为None则覆盖原文件
    """
    
    if output_file_path is None:
        output_file_path = input_file_path
    
    print(f"开始处理文件: {input_file_path}")
    
    try:
        # 使用openpyxl加载工作簿
        workbook = load_workbook(input_file_path)
        
        # 检查是否存在"清单"工作表
        if "清单" not in workbook.sheetnames:
            print("错误：未找到'清单'工作表")
            return False
        
        # 获取清单工作表
        sheet = workbook["清单"]
        
        # 找到"处理过程"列的位置
        process_col_index = None
        header_row = 1
        
        for col in range(1, sheet.max_column + 1):
            cell_value = sheet.cell(row=header_row, column=col).value
            if cell_value == "处理过程":
                process_col_index = col
                break
        
        if process_col_index is None:
            print("错误：未找到'处理过程'列")
            return False
        
        print(f"找到'处理过程'列，位置：第{process_col_index}列")
        
        # 找到或创建"处理内容"和"反馈结果"列
        # 先检查是否已存在这些列
        content_col_index = None
        feedback_col_index = None
        
        for col in range(1, sheet.max_column + 1):
            cell_value = sheet.cell(row=header_row, column=col).value
            if cell_value == "处理内容":
                content_col_index = col
            elif cell_value == "反馈结果":
                feedback_col_index = col
        
        # 如果不存在，则在最后添加新列
        if content_col_index is None:
            content_col_index = sheet.max_column + 1
            sheet.cell(row=header_row, column=content_col_index, value="处理内容")
            print(f"创建'处理内容'列，位置：第{content_col_index}列")
        
        if feedback_col_index is None:
            feedback_col_index = sheet.max_column + 1
            sheet.cell(row=header_row, column=feedback_col_index, value="反馈结果")
            print(f"创建'反馈结果'列，位置：第{feedback_col_index}列")
        
        # 处理数据行
        processed_count = 0
        
        for row in range(2, sheet.max_row + 1):  # 从第2行开始处理数据
            # 获取处理过程列的内容
            process_text = sheet.cell(row=row, column=process_col_index).value
            
            if process_text and isinstance(process_text, str):
                # 提取处理内容
                content = extract_content_between_markers(process_text)
                if content:
                    sheet.cell(row=row, column=content_col_index, value=content)
                
                # 提取反馈结果
                feedback = extract_feedback_result(process_text)
                if feedback:
                    sheet.cell(row=row, column=feedback_col_index, value=feedback)
                
                if content or feedback:
                    processed_count += 1
            
            # 每处理100行显示一次进度
            if row % 100 == 0:
                print(f"已处理到第 {row} 行...")
        
        # 保存文件
        workbook.save(output_file_path)
        print(f"处理完成！")
        print(f"总共处理了 {processed_count} 行有效数据")
        print(f"结果已保存到: {output_file_path}")
        
        return True
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        return False


def main():
    """主函数"""
    # 配置文件路径
    input_file = "./output/test.xlsx"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：输入文件不存在 - {input_file}")
        return
    
    # 处理文件
    success = process_excel_file(input_file)
    
    if success:
        print("✅ 数据处理成功完成！")
    else:
        print("❌ 数据处理失败！")


if __name__ == "__main__":
    main()
