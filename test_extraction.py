#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试提取函数的脚本
"""

import re


def extract_content_between_markers(text):
    """
    基于实际数据格式提取处理内容
    从"一、处理结果："或"一、处理情况："部分提取内容
    """
    if not text or not isinstance(text, str):
        return ""

    # 尝试提取"一、处理结果："后的内容
    pattern1 = r'一、处理结果：(.*?)(?=二、|三、|$)'
    match1 = re.search(pattern1, text, re.DOTALL)

    if match1:
        content = match1.group(1).strip()
        # 清理内容，移除多余的换行和空格
        content = re.sub(r'\s+', ' ', content)
        return content

    # 如果没找到"处理结果"，尝试"处理情况"
    pattern2 = r'一、处理情况：(.*?)(?=二、|三、|$)'
    match2 = re.search(pattern2, text, re.DOTALL)

    if match2:
        content = match2.group(1).strip()
        content = re.sub(r'\s+', ' ', content)
        return content

    return ""


def extract_feedback_result(text):
    """
    基于实际数据格式提取反馈结果
    从"二、核查情况："部分提取内容
    """
    if not text or not isinstance(text, str):
        return ""

    # 提取"二、核查情况："后的内容
    pattern = r'二、核查情况：(.*?)(?=三、|$)'
    match = re.search(pattern, text, re.DOTALL)

    if match:
        content = match.group(1).strip()
        # 清理内容，移除多余的换行和空格
        content = re.sub(r'\s+', ' ', content)
        return content

    return ""


# 测试数据（从实际Excel文件中获取的样本）
test_data = [
    """【类型二】我部核查结果如下：
一、处理结果：OAO流量包集团仅负责短信下发。 办理页面 宣传均非集团全渠业务，请省内自行处理。
二、核查情况：【办理提醒】验证码：907531，您正在 通过中国电信APP， 订购39元大流量月包，该产品资费：39元/月，含每月20GB国内通用流量，包内流量可共享。订购立即生效，默认按月自动续订，退订次月生效，确保本人操作，验证码切勿告知他人， 以免隐私泄露，此验证码 3分钟有效。订购内容详见《订购说明》。
三、处理过程：/
 请省内知悉，如对我侧回单内容有疑问可在QQ群（安徽-集团电渠客服：438910903）联系工单处理员：王星，集 团对省邮箱*************，谢谢！""",

    """【类型二】我部核查结果如下：
一、处理情况：省内0A0流量包，只有二次验证是调用集团全渠短信接口下发，此流量包为省内宣传开展，有争议请自行联系省订单处理中心核实处理，集团全渠不再介入处理。
二、核查情况： S5S04125022216291405426834 0.00  已完成 安徽 可* 177 **** 9296 在线支付 Wap厅 189.OAO.OAO.cc-010154002001
三、处理过程：【办理提醒】验证码：75**94，您正在通过中国电信APP， 订购39元大流量月包，该产品资费：39元/月，含每月20GB国内通用流量，包内流量可共享。订购立即生效，默认按月自动续订，退订次月生效，确保本人操作，验证码切勿告知他人，以免隐私泄露，此验证码 3分钟有效。订购内容详见《订购说明》。""",

    """ 我部核查结果如下：
一、处理结果：此为OAO流量包，请省内自查。费用由省内扣除，集团全渠侧不予退款。针对用户使用情况，省内可酌情协调用户处理。
二、核查情况：【办理提醒】验证码：135991，您正在通过中国电信APP， 订购39元大流量月包，该产品大流量月包20GB39元（订购立即生效，主副卡可共享，按月自动续订），确保本人操作，验证码切勿告知他人，以免隐私泄露，此验证码 3分钟有效。订购内容详见《订购说明》。"""
]

def main():
    print("=== 测试提取函数 ===\n")
    
    for i, text in enumerate(test_data, 1):
        print(f"测试样本 {i}:")
        print(f"原文本: {text[:100]}...")
        print()
        
        # 测试提取处理内容
        content = extract_content_between_markers(text)
        print(f"提取的处理内容: '{content}'")
        
        # 测试提取反馈结果
        feedback = extract_feedback_result(text)
        print(f"提取的反馈结果: '{feedback}'")
        
        print("-" * 80)
        print()


if __name__ == "__main__":
    main()
